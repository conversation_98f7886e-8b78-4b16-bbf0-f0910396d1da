import os
import logging
from PIL import Image
import io
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image as RLImage, PageBreak
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_LEFT, TA_CENTER
from reportlab.lib import colors

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AcademicPDFGenerator:
    def __init__(self, output_path, logo_path, content, page_size='A4', orientation='portrait'):
        """Initialize the PDF generator with logo, content, and page settings."""
        self.output_path = output_path
        self.logo_path = logo_path
        self.content = content
        self.page_size = A4 if page_size.lower() == 'a4' else letter
        self.orientation = orientation
        self.styles = self._setup_styles()
        self.logo = None
        self.validate_inputs()

    def _setup_styles(self):
        """Define styles for headings, paragraphs, and bullet points."""
        styles = getSampleStyleSheet()

        # Add custom styles (avoiding duplicate names)
        styles.add(ParagraphStyle(
            name='ChapterHeading',
            fontName='Times-Bold',
            fontSize=16,
            leading=20,
            spaceAfter=12,
            alignment=TA_LEFT
        ))
        styles.add(ParagraphStyle(
            name='SubHeading',
            fontName='Times-Bold',
            fontSize=12,
            leading=15,
            spaceAfter=8,
            alignment=TA_LEFT
        ))

        # Modify the existing BodyText style instead of adding a duplicate
        styles['BodyText'].fontName = 'Times-Roman'
        styles['BodyText'].fontSize = 11
        styles['BodyText'].leading = 14
        styles['BodyText'].spaceAfter = 10
        styles['BodyText'].alignment = TA_LEFT

        styles.add(ParagraphStyle(
            name='BulletPoint',
            fontName='Times-Roman',
            fontSize=11,
            leading=14,
            leftIndent=20,
            firstLineIndent=-10,
            spaceAfter=6,
            bulletFontName='Times-Roman',
            bulletFontSize=11,
            bulletIndent=10,
            alignment=TA_LEFT
        ))
        return styles

    def validate_inputs(self):
        """Validate input logo file and content."""
        logger.info("Validating inputs...")
        # Validate logo file
        if not os.path.exists(self.logo_path):
            logger.error(f"Logo file not found: {self.logo_path}")
            raise FileNotFoundError(f"Logo file not found: {self.logo_path}")
        
        try:
            self.logo = Image.open(self.logo_path)
            if self.logo.format not in ['PNG', 'JPEG', 'SVG']:
                logger.warning(f"Unsupported image format: {self.logo.format}. Converting to PNG.")
                self.logo = self.logo.convert('RGB')
        except Exception as e:
            logger.error(f"Invalid logo image: {str(e)}")
            raise ValueError(f"Invalid logo image: {str(e)}")

        # Validate content
        if not isinstance(self.content, list):
            logger.error("Content must be a list of dictionaries")
            raise ValueError("Content must be a list of dictionaries")
        
        for item in self.content:
            if not isinstance(item, dict) or 'type' not in item or 'content' not in item:
                logger.error("Each content item must be a dictionary with 'type' and 'content' keys")
                raise ValueError("Each content item must be a dictionary with 'type' and 'content' keys")
            if item['type'] not in ['chapter', 'subheading', 'paragraph', 'bullet_list']:
                logger.error(f"Invalid content type: {item['type']}")
                raise ValueError(f"Invalid content type: {item['type']}")

        logger.info("Input validation completed successfully")

    def _resize_logo(self, max_width=2*inch, max_height=1*inch):
        """Resize logo while preserving aspect ratio."""
        logo_width, logo_height = self.logo.size
        aspect = logo_width / logo_height

        if logo_width > max_width or logo_height > max_height:
            if aspect > 1:  # Wider than tall
                new_width = min(max_width, logo_width)
                new_height = new_width / aspect
            else:  # Taller than wide
                new_height = min(max_height, logo_height)
                new_width = new_height * aspect
            self.logo = self.logo.resize((int(new_width), int(new_height)), Image.Resampling.LANCZOS)
        return self.logo

    def _create_logo_flowable(self):
        """Create a flowable for the logo in the header."""
        logo = self._resize_logo()
        logo_buffer = io.BytesIO()
        logo.save(logo_buffer, format='PNG')
        logo_buffer.seek(0)
        return RLImage(logo_buffer, width=logo.size[0], height=logo.size[1])

    def build_pdf(self):
        """Generate the PDF document."""
        logger.info(f"Generating PDF: {self.output_path}")
        doc = SimpleDocTemplate(
            self.output_path,
            pagesize=self.page_size,
            rightMargin=0.75*inch,
            leftMargin=0.75*inch,
            topMargin=1.5*inch,
            bottomMargin=0.75*inch
        )

        story = []

        # Add logo to the first page
        logo_flowable = self._create_logo_flowable()
        story.append(logo_flowable)
        story.append(Spacer(1, 0.25*inch))

        # Process content
        for item in self.content:
            content_type = item['type']
            content_data = item['content']

            if content_type == 'chapter':
                story.append(Paragraph(content_data, self.styles['ChapterHeading']))
                story.append(Spacer(1, 0.2*inch))
            elif content_type == 'subheading':
                story.append(Paragraph(content_data, self.styles['SubHeading']))
                story.append(Spacer(1, 0.1*inch))
            elif content_type == 'paragraph':
                story.append(Paragraph(content_data, self.styles['BodyText']))
                story.append(Spacer(1, 0.1*inch))
            elif content_type == 'bullet_list':
                for bullet in content_data:
                    story.append(Paragraph(f"• {bullet}", self.styles['BulletPoint']))
                story.append(Spacer(1, 0.1*inch))
            else:
                logger.warning(f"Skipping unknown content type: {content_type}")

        try:
            doc.build(story)
            logger.info(f"PDF generated successfully: {self.output_path}")
        except Exception as e:
            logger.error(f"Failed to generate PDF: {str(e)}")
            raise RuntimeError(f"Failed to generate PDF: {str(e)}")

def generate_academic_pdf(logo_path, content, output_path="output.pdf", page_size='A4', orientation='portrait'):
    """Generate an academic PDF with the given logo and content."""
    try:
        generator = AcademicPDFGenerator(output_path, logo_path, content, page_size, orientation)
        generator.build_pdf()
        return f"PDF generated successfully at {output_path}"
    except Exception as e:
        logger.error(f"Error generating PDF: {str(e)}")
        return f"Error generating PDF: {str(e)}"

# Example usage
if __name__ == "__main__":
    sample_content = [
        {'type': 'chapter', 'content': 'Chapter 1: Introduction'},
        {'type': 'paragraph', 'content': 'This report provides an overview of key concepts in academic research. It aims to guide educators in creating structured study materials.'},
        {'type': 'subheading', 'content': 'Key Objectives'},
        {'type': 'bullet_list', 'content': [
            'Understand core principles of the subject',
            'Develop critical thinking skills',
            'Apply knowledge in practical scenarios'
        ]},
        {'type': 'paragraph', 'content': 'The following sections will delve into detailed methodologies and case studies to support academic learning.'},
        {'type': 'chapter', 'content': 'Chapter 2: Methodology'},
        {'type': 'paragraph', 'content': 'This chapter outlines the research methods used in the study, including data collection and analysis techniques.'}
    ]

    logo_path = r"C:\Users\<USER>\Downloads\netskill_kikli.png"  # Replace with actual logo path
    output_path = "academic_report.pdf"
    result = generate_academic_pdf(logo_path, sample_content, output_path)
    print(result)